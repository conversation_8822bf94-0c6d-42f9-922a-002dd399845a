#!/usr/bin/env python3
"""
多模态绝缘子检测训练入口脚本
修复版本 - 使用正确的类别数（nc=5）
"""

import os
import sys
import yaml
import argparse
from datetime import datetime

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)
sys.path.insert(0, current_dir)

# 导入训练模块
from src.training.train_multimodal import MultimodalTrainer

def create_fixed_config():
    """创建修复后的训练配置，支持渐进式训练"""
    config = {
        'data_dir': 'data',
        'output_dir': f'runs/train_progressive/stage1_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
        'nc': 5,  # 总类别数
        'img_size': 512,
        'batch_size': 4,  # 降低批次大小提高稳定性
        'epochs': 25,  # 第一阶段轮数
        'num_workers': 0,  # Windows兼容
        'grad_clip': 5.0,  # 降低梯度裁剪阈值

        # 渐进式训练配置
        'progressive_mode': True,
        'current_stage': 1,
        'active_classes': [0, 1, 3],  # 第一阶段：主要类别（87.9%数据）

        'model': {
            'type': 'simple',  # 使用simple类型，内部会创建FixedMultimodalYOLO
            'yolo_model_path': 'yolov8n.pt',
            'fusion_type': 'cross_attention'
        },

        'optimizer': {
            'type': 'Adam',
            'lr': 0.00005,  # 进一步降低学习率
            'weight_decay': 0.0001
        },

        'scheduler': {
            'type': 'CosineAnnealingLR',
            'T_max': 25,  # 第一阶段轮数
            'eta_min': 1e-6
        },

        'early_stopping': {
            'patience': 12  # 增加耐心值
        },

        # 使用Focal Loss处理类别不平衡
        'use_focal_loss': True,

        # 损失权重配置
        'loss_weights': {
            'box_loss': 1.0,
            'obj_loss': 2.0,  # 降低置信度损失权重
            'cls_loss': 1.0
        }
    }
    return config

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='多模态绝缘子检测训练（修复版）')
    parser.add_argument('--config', type=str, help='配置文件路径（可选）')
    parser.add_argument('--epochs', type=int, help='训练轮数')
    parser.add_argument('--batch_size', type=int, help='批次大小')
    parser.add_argument('--lr', type=float, help='学习率')

    args = parser.parse_args()

    print("🚀 [TRAIN] 启动渐进式多模态绝缘子检测训练...")
    print(f"📁 [INFO] 项目根目录: {current_dir}")

    print("\n🎯 [STRATEGY] 渐进式训练策略:")
    print("  阶段1 (25轮): 训练主要类别 [0,1,3] (87.9%数据)")
    print("  - 解决训练不稳定问题")
    print("  - 建立稳定的特征表示")
    print("  阶段2: 手动切换到类别 [0,1,2,3]")
    print("  阶段3: 手动切换到全类别 [0,1,2,3,4]")

    print("\n✅ [IMPROVEMENTS] 关键改进:")
    print("  ✅ Focal Loss - 处理类别不平衡")
    print("  ✅ 类别权重平衡 - 提升少数类别")
    print("  ✅ 降低学习率 - 提高训练稳定性")
    print("  ✅ 降低批次大小 - 减少内存压力")
    print("  ✅ 梯度裁剪 - 防止梯度爆炸")
    print("-" * 60)

    # 加载配置
    if args.config and os.path.exists(args.config):
        print(f"📄 [CONFIG] 加载配置文件: {args.config}")
        with open(args.config, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        # 转换配置格式以匹配训练器期望的格式
        if 'training' in config:
            training_config = {
                'data_dir': config.get('data_dir', 'data'),
                'output_dir': 'runs/train_fixed',
                'nc': config['model']['nc'],
                'img_size': config.get('img_size', 640),
                'batch_size': config.get('batch_size', 16),
                'epochs': config['training']['epochs'],
                'num_workers': config.get('num_workers', 0),
                'grad_clip': 10.0,

                'model': {
                    'type': 'simple',
                    'yolo_model_path': 'yolov8n.pt',
                    'fusion_type': config['model']['fusion_type']
                },

                'optimizer': {
                    'type': 'Adam',
                    'lr': config['training']['lr'],
                    'weight_decay': config['training']['weight_decay']
                },

                'scheduler': {
                    'type': 'CosineAnnealingLR',
                    'T_max': config['training']['epochs']
                },

                'early_stopping': {
                    'patience': config['training']['patience']
                }
            }
        else:
            training_config = config
    else:
        print("📄 [CONFIG] 使用默认修复配置")
        training_config = create_fixed_config()

    # 命令行参数覆盖
    if args.epochs:
        training_config['epochs'] = args.epochs
        print(f"🔧 [OVERRIDE] 训练轮数: {args.epochs}")
    if args.batch_size:
        training_config['batch_size'] = args.batch_size
        print(f"🔧 [OVERRIDE] 批次大小: {args.batch_size}")
    if args.lr:
        training_config['optimizer']['lr'] = args.lr
        print(f"🔧 [OVERRIDE] 学习率: {args.lr}")

    # 确保类别数正确
    if training_config['nc'] != 5:
        print(f"⚠️ [WARNING] 检测到nc={training_config['nc']}，强制修正为nc=5")
        training_config['nc'] = 5

    # 确保渐进式训练配置存在
    if 'progressive_mode' not in training_config:
        training_config['progressive_mode'] = False
    if 'current_stage' not in training_config:
        training_config['current_stage'] = 1
    if 'active_classes' not in training_config:
        training_config['active_classes'] = list(range(training_config['nc']))
    if 'use_focal_loss' not in training_config:
        training_config['use_focal_loss'] = False

    # Windows系统下强制设置num_workers=0
    import platform
    if platform.system() == 'Windows':
        print("🔧 [INFO] Windows系统检测到，自动设置 num_workers=0")
        training_config['num_workers'] = 0

    # 创建输出目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    training_config['output_dir'] = os.path.join(training_config['output_dir'], f'fixed_{timestamp}')
    os.makedirs(training_config['output_dir'], exist_ok=True)

    # 保存配置到输出目录
    config_save_path = os.path.join(training_config['output_dir'], 'config.yaml')
    with open(config_save_path, 'w', encoding='utf-8') as f:
        yaml.dump(training_config, f, default_flow_style=False, allow_unicode=True)

    print("\n📋 [CONFIG] 训练配置:")
    print(f"  数据目录: {training_config['data_dir']}")
    print(f"  输出目录: {training_config['output_dir']}")
    print(f"  类别数: {training_config['nc']}")
    print(f"  训练轮数: {training_config['epochs']}")
    print(f"  批次大小: {training_config['batch_size']}")
    print(f"  学习率: {training_config['optimizer']['lr']}")
    print(f"  图像尺寸: {training_config['img_size']}")

    print("\n🔧 [PROGRESSIVE] 渐进式训练配置:")
    print(f"  当前阶段: {training_config.get('current_stage', 1)}")
    print(f"  活跃类别: {training_config.get('active_classes', list(range(5)))}")
    print(f"  使用Focal Loss: {training_config.get('use_focal_loss', False)}")
    print("  ✅ 检测头权重正确初始化")
    print("  ✅ 置信度偏置设为-4.6")
    print("  ✅ 类别权重平衡")
    print("  ✅ 使用FixedMultimodalYOLO模型")
    print("  ✅ 优化学习率和权重衰减")

    # 开始训练
    try:
        print("\n🚀 [START] 开始训练...")
        trainer = MultimodalTrainer(training_config)
        trainer.train()
        print("🎉 [SUCCESS] 阶段1训练完成！")
        print(f"\n📁 [OUTPUT] 输出目录: {training_config['output_dir']}")
        print(f"💾 [MODEL] 最佳模型: {training_config['output_dir']}/weights/best.pt")
        print(f"📊 [LOGS] TensorBoard日志: {training_config['output_dir']}/logs")

        print(f"\n🔍 [EVALUATION] 评估阶段1结果:")
        print(f"  python evaluate.py --model_path {training_config['output_dir']}/weights/best.pt")

        print(f"\n🎯 [NEXT STEPS] 继续渐进式训练:")
        print("  1. 修改配置进行阶段2训练:")
        print("     - active_classes: [0, 1, 2, 3]")
        print("     - epochs: 20")
        print("     - 加载阶段1的最佳权重")
        print("  2. 最后进行阶段3全类别训练:")
        print("     - active_classes: [0, 1, 2, 3, 4]")
        print("     - epochs: 35")
        print("     - 使用更低学习率进行精调")

        print(f"\n📊 [EXPECTED] 预期效果:")
        print("  - 解决训练不稳定问题")
        print("  - 显著提高少数类别检测性能")
        print("  - mAP从0.15提升到0.6+")

    except Exception as e:
        print(f"❌ [ERROR] 训练失败: {e}")
        import traceback
        traceback.print_exc()