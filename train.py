#!/usr/bin/env python3
"""
多模态绝缘子检测训练入口脚本
修复版本 - 使用正确的类别数（nc=5）
"""

import os
import sys
import yaml
import argparse
from datetime import datetime

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
sys.path.insert(0, src_dir)
sys.path.insert(0, current_dir)

# 导入训练模块
from src.training.train_multimodal import MultimodalTrainer

def create_fixed_config():
    """创建修复后的训练配置，确保nc=5"""
    config = {
        'data_dir': 'data',
        'output_dir': 'runs/train_fixed',
        'nc': 5,  # 修复：确保类别数为5
        'img_size': 640,
        'batch_size': 16,
        'epochs': 100,
        'num_workers': 0,  # Windows兼容
        'grad_clip': 10.0,

        'model': {
            'type': 'simple',  # 使用simple类型，内部会创建FixedMultimodalYOLO
            'yolo_model_path': 'yolov8n.pt',
            'fusion_type': 'cross_attention'
        },

        'optimizer': {
            'type': 'Adam',
            'lr': 0.0001,  # 降低学习率
            'weight_decay': 0.0001
        },

        'scheduler': {
            'type': 'CosineAnnealingLR',
            'T_max': 100
        },

        'early_stopping': {
            'patience': 15
        }
    }
    return config

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='多模态绝缘子检测训练（修复版）')
    parser.add_argument('--config', type=str, help='配置文件路径（可选）')
    parser.add_argument('--epochs', type=int, help='训练轮数')
    parser.add_argument('--batch_size', type=int, help='批次大小')
    parser.add_argument('--lr', type=float, help='学习率')

    args = parser.parse_args()

    print("🚀 [TRAIN] 启动修复版多模态绝缘子检测训练...")
    print(f"📁 [INFO] 项目根目录: {current_dir}")
    print("🔧 [INFO] 使用修复后的配置（nc=5）")
    print("-" * 60)

    # 加载配置
    if args.config and os.path.exists(args.config):
        print(f"📄 [CONFIG] 加载配置文件: {args.config}")
        with open(args.config, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        # 转换配置格式以匹配训练器期望的格式
        if 'training' in config:
            training_config = {
                'data_dir': config.get('data_dir', 'data'),
                'output_dir': 'runs/train_fixed',
                'nc': config['model']['nc'],
                'img_size': config.get('img_size', 640),
                'batch_size': config.get('batch_size', 16),
                'epochs': config['training']['epochs'],
                'num_workers': config.get('num_workers', 0),
                'grad_clip': 10.0,

                'model': {
                    'type': 'simple',
                    'yolo_model_path': 'yolov8n.pt',
                    'fusion_type': config['model']['fusion_type']
                },

                'optimizer': {
                    'type': 'Adam',
                    'lr': config['training']['lr'],
                    'weight_decay': config['training']['weight_decay']
                },

                'scheduler': {
                    'type': 'CosineAnnealingLR',
                    'T_max': config['training']['epochs']
                },

                'early_stopping': {
                    'patience': config['training']['patience']
                }
            }
        else:
            training_config = config
    else:
        print("📄 [CONFIG] 使用默认修复配置")
        training_config = create_fixed_config()

    # 命令行参数覆盖
    if args.epochs:
        training_config['epochs'] = args.epochs
        print(f"🔧 [OVERRIDE] 训练轮数: {args.epochs}")
    if args.batch_size:
        training_config['batch_size'] = args.batch_size
        print(f"🔧 [OVERRIDE] 批次大小: {args.batch_size}")
    if args.lr:
        training_config['optimizer']['lr'] = args.lr
        print(f"🔧 [OVERRIDE] 学习率: {args.lr}")

    # 确保类别数正确
    if training_config['nc'] != 5:
        print(f"⚠️ [WARNING] 检测到nc={training_config['nc']}，强制修正为nc=5")
        training_config['nc'] = 5

    # Windows系统下强制设置num_workers=0
    import platform
    if platform.system() == 'Windows':
        print("🔧 [INFO] Windows系统检测到，自动设置 num_workers=0")
        training_config['num_workers'] = 0

    # 创建输出目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    training_config['output_dir'] = os.path.join(training_config['output_dir'], f'fixed_{timestamp}')
    os.makedirs(training_config['output_dir'], exist_ok=True)

    # 保存配置到输出目录
    config_save_path = os.path.join(training_config['output_dir'], 'config.yaml')
    with open(config_save_path, 'w', encoding='utf-8') as f:
        yaml.dump(training_config, f, default_flow_style=False, allow_unicode=True)

    print("\n📋 [CONFIG] 训练配置:")
    print(f"  数据目录: {training_config['data_dir']}")
    print(f"  输出目录: {training_config['output_dir']}")
    print(f"  类别数: {training_config['nc']}")
    print(f"  训练轮数: {training_config['epochs']}")
    print(f"  批次大小: {training_config['batch_size']}")
    print(f"  学习率: {training_config['optimizer']['lr']}")
    print(f"  图像尺寸: {training_config['img_size']}")

    print("\n🔧 [FIXES] 关键修复:")
    print("  ✅ 检测头权重正确初始化")
    print("  ✅ 置信度偏置设为-4.6")
    print("  ✅ 类别数设为5")
    print("  ✅ 使用FixedMultimodalYOLO模型")
    print("  ✅ 优化学习率和权重衰减")

    # 开始训练
    try:
        print("\n🚀 [START] 开始训练...")
        trainer = MultimodalTrainer(training_config)
        trainer.train()
        print("🎉 [SUCCESS] 训练完成！")
    except Exception as e:
        print(f"❌ [ERROR] 训练失败: {e}")
        import traceback
        traceback.print_exc()